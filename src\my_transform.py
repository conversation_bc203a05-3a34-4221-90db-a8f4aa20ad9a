import time
import numpy as np
import cv2


def rectify(std_img, targ_img, max_iter=10, is_debug=False):
    # 使用ORB特征检测器替代SURF
    orb = cv2.ORB_create(nfeatures=5000)

    # 计算keypoints和描述子
    kp1, des1 = orb.detectAndCompute(std_img, None)
    kp2, des2 = orb.detectAndCompute(targ_img, None)

    # 检查描述子是否为空
    if des1 is None or des2 is None:
        print("无法检测到足够的特征点")
        return targ_img

    # 暴力匹配 - ORB使用汉明距离
    bf = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=False)
    matches = bf.knnMatch(des1, des2, k=2)

    # 调优 - ORB需要不同的阈值
    good = []
    for match_pair in matches:
        if len(match_pair) == 2:
            m, n = match_pair
            if m.distance < 0.7 * n.distance:
                good.append(m)

    # 检查是否有足够的匹配点
    if len(good) < 4:
        print(f"匹配点不足: {len(good)}, 需要至少4个点进行透视变换")
        return targ_img

    good_kp1, good_kp2, tmp = [], [], []
    for i in range(len(good)):
        tmp.append(cv2.DMatch(i, i, 0))
        good_kp1.append(kp1[good[i].queryIdx])
        good_kp2.append(kp2[good[i].trainIdx])

    pt1 = [kp.pt for kp in good_kp1]
    pt2 = [kp.pt for kp in good_kp2]

    # 计算单应性矩阵
    M, mask = cv2.findHomography(np.float32(pt2), np.float32(pt1), cv2.RANSAC, 1.0)
    
    if M is None:
        print("无法计算单应性矩阵")
        return targ_img
        
    dst = cv2.warpPerspective(targ_img, M, (int(targ_img.shape[1]), int(targ_img.shape[0])))
    # cv2.imwrite("std_wrap.jpeg", dst)

    if is_debug:
        dbg = cv2.drawMatches(std_img, good_kp1, targ_img, good_kp2, tmp, None, flags=2)
        # 保存调试图像而不是显示
        cv2.imwrite("debug_matches.jpeg", dbg)
        print(f"调试图像已保存为 debug_matches.jpeg，匹配点数量: {len(good)}")
    return dst